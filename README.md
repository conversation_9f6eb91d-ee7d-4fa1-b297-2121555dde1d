# ConquerInjector

مشروع ConquerInjector هو تطبيق Windows Forms مكتوب بـ C# يستهدف .NET Framework 4.8 لحقن الكود في عمليات لعبة Conquer.

## الميزات

- **البحث عن العمليات**: يبحث عن عمليات لعبة Conquer النشطة
- **قراءة أسماء اللاعبين**: يقرأ أسماء اللاعبين من ذاكرة اللعبة
- **حقن الكود**: يحقن كود assembly مخصص في عمليات اللعبة
- **إدارة الذاكرة**: يدير تخصيص الذاكرة والكتابة فيها
- **واجهة مستخدم عربية**: واجهة مستخدم باللغة العربية

## متطلبات النظام

- Windows 10/11
- .NET Framework 4.8
- Visual Studio 2019/2022 أو .NET SDK

## بناء المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd ConquerInjector

# بناء المشروع
dotnet build

# تشغيل المشروع
dotnet run
```

## استخدام التطبيق

1. **تشغيل التطبيق**: قم بتشغيل ConquerInjector.exe
2. **تحديث العمليات**: اضغط على زر "تحديث" للبحث عن عمليات Conquer
3. **اختيار العملية**: اختر العملية المطلوبة من القائمة
4. **تفعيل السكربت**: اضغط على "تفعيل" لحقن الكود
5. **إيقاف السكربت**: اضغط على "إيقاف" لإزالة الحقن

## بنية المشروع

- `Program.cs`: نقطة دخول التطبيق
- `Form1.cs`: الواجهة الرئيسية والمنطق
- `Form1.Designer.cs`: تصميم الواجهة
- `MemoryHelper.cs`: مساعد إدارة الذاكرة والعمليات
- `AssemblerHelper.cs`: مساعد تجميع كود Assembly

## الأمان

⚠️ **تحذير**: هذا التطبيق يستخدم تقنيات حقن الكود وتعديل الذاكرة. استخدمه بحذر وعلى مسؤوليتك الخاصة.

## الترخيص

هذا المشروع مخصص للأغراض التعليمية فقط.
