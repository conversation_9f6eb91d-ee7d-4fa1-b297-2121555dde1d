# ConquerInjector

مشروع ConquerInjector هو تطبيق Windows Forms مكتوب بـ C# يستهدف .NET Framework 4.8 لحقن الكود في عمليات لعبة Conquer.

## الميزات

- **البحث عن العمليات**: يبحث عن عمليات لعبة Conquer النشطة
- **قراءة أسماء اللاعبين**: يقرأ أسماء اللاعبين من ذاكرة اللعبة
- **حقن الكود**: يحقن كود assembly مخصص في عمليات اللعبة
- **إدارة الذاكرة المحسنة**: يدير تخصيص وتحرير الذاكرة بكفاءة
- **البحث عن الأنماط المحسن**: خوارزمية بحث AOB محسنة مع دعم الـ chunks
- **مجمع Assembly مخصص**: مجمع مخصص يدعم تعليمات x86 المطلوبة
- **واجهة مستخدم عربية**: واجهة مستخدم باللغة العربية مع سجل العمليات

## متطلبات النظام

- Windows 10/11
- .NET Framework 4.8
- Visual Studio 2019/2022 أو .NET SDK

## بناء المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd ConquerInjector

# بناء المشروع
dotnet build

# تشغيل المشروع
dotnet run
```

## استخدام التطبيق

1. **تشغيل التطبيق**: قم بتشغيل ConquerInjector.exe
2. **تحديث العمليات**: اضغط على زر "تحديث" للبحث عن عمليات Conquer
3. **اختيار العملية**: اختر العملية المطلوبة من القائمة
4. **تفعيل السكربت**: اضغط على "تفعيل" لحقن الكود
5. **إيقاف السكربت**: اضغط على "إيقاف" لإزالة الحقن

## بنية المشروع

- `Program.cs`: نقطة دخول التطبيق
- `Form1.cs`: الواجهة الرئيسية والمنطق مع دعم كامل للحقن
- `Form1.Designer.cs`: تصميم الواجهة العربية
- `MemoryHelper.cs`: مساعد إدارة الذاكرة مع دوال محسنة للبحث والحماية
- `AssemblerHelper.cs`: مجمع Assembly مخصص يدعم تعليمات x86 المطلوبة

## التحسينات المطبقة

✅ **دالة FindPattern محسنة**: تستخدم chunks لتجنب استهلاك الذاكرة الكبير
✅ **مجمع Assembly مخصص**: يدعم جميع التعليمات المطلوبة في السكربت
✅ **إدارة ذاكرة محسنة**: مع دوال تحرير الذاكرة وحماية المناطق
✅ **العناوين والقيم مضمونة**: كما هي من السكربت الأصلي
✅ **معالجة الأخطاء**: مع رسائل خطأ واضحة وتنظيف الموارد

## الأمان

⚠️ **تحذير**: هذا التطبيق يستخدم تقنيات حقن الكود وتعديل الذاكرة. استخدمه بحذر وعلى مسؤوليتك الخاصة.

## الترخيص

هذا المشروع مخصص للأغراض التعليمية فقط.
