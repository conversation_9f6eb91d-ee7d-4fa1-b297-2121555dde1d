Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ConquerInjector", "ConquerInjector.csproj", "{893DCC08-4CF5-F909-F4E8-541B1C02EB95}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{893DCC08-4CF5-F909-F4E8-541B1C02EB95}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{893DCC08-4CF5-F909-F4E8-541B1C02EB95}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{893DCC08-4CF5-F909-F4E8-541B1C02EB95}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{893DCC08-4CF5-F909-F4E8-541B1C02EB95}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A961DE63-929E-4EB4-B3F0-33FB65A6D16E}
	EndGlobalSection
EndGlobal
