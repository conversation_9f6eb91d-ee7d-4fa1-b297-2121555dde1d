using System;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace ConquerInjector
{
    public class MemoryHelper
    {
        [Flags]
        public enum ProcessAccessFlags : uint
        {
            All = 0x001F0FFF
        }

        [DllImport("kernel32.dll")]
        static extern IntPtr OpenProcess(ProcessAccessFlags dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] lpBuffer, int dwSize, out IntPtr lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] lpBuffer, int dwSize, out IntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint dwFreeType);

        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool VirtualProtectEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flNewProtect, out uint lpflOldProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool CloseHandle(IntPtr hObject);

        private IntPtr processHandle = IntPtr.Zero;

        public bool OpenProcessById(int pid)
        {
            processHandle = OpenProcess(ProcessAccessFlags.All, false, pid);
            return processHandle != IntPtr.Zero;
        }

        public IntPtr AllocMemory(int size)
        {
            return VirtualAllocEx(processHandle, IntPtr.Zero, (uint)size, 0x1000 | 0x2000, 0x40);
        }

        public void WriteMemory(IntPtr address, byte[] data)
        {
            WriteProcessMemory(processHandle, address, data, data.Length, out _);
        }

        public string ReadPlayerName(Process proc, IntPtr address)
        {
            byte[] buffer = new byte[32];
            ReadProcessMemory(processHandle, address, buffer, buffer.Length, out _);
            return Encoding.ASCII.GetString(buffer).TrimEnd('\0');
        }

        public IntPtr AobScan(Process process, byte[] pattern, string mask)
        {
            try
            {
                foreach (ProcessModule module in process.Modules)
                {
                    IntPtr start = module.BaseAddress;
                    int size = module.ModuleMemorySize;

                    // Read memory in chunks to avoid large allocations
                    int chunkSize = 0x10000; // 64KB chunks
                    for (int offset = 0; offset < size; offset += chunkSize)
                    {
                        int currentChunkSize = Math.Min(chunkSize, size - offset);
                        byte[] buffer = new byte[currentChunkSize];

                        if (!ReadProcessMemory(processHandle, start + offset, buffer, currentChunkSize, out _))
                            continue;

                        // Search for pattern in current chunk
                        for (int i = 0; i <= currentChunkSize - pattern.Length; i++)
                        {
                            bool found = true;
                            for (int j = 0; j < pattern.Length; j++)
                            {
                                if (mask[j] == 'x' && buffer[i + j] != pattern[j])
                                {
                                    found = false;
                                    break;
                                }
                            }
                            if (found)
                            {
                                return start + offset + i;
                            }
                        }

                        // Handle pattern that might span across chunks
                        if (offset + currentChunkSize < size && currentChunkSize >= pattern.Length)
                        {
                            // Check overlap region
                            int overlapStart = currentChunkSize - pattern.Length + 1;
                            for (int i = overlapStart; i < currentChunkSize; i++)
                            {
                                bool found = true;
                                for (int j = 0; j < pattern.Length; j++)
                                {
                                    int bufferIndex = i + j;
                                    if (bufferIndex >= currentChunkSize)
                                    {
                                        // Read next byte from next chunk
                                        byte[] nextByte = new byte[1];
                                        if (!ReadProcessMemory(processHandle, start + offset + bufferIndex, nextByte, 1, out _))
                                        {
                                            found = false;
                                            break;
                                        }
                                        if (mask[j] == 'x' && nextByte[0] != pattern[j])
                                        {
                                            found = false;
                                            break;
                                        }
                                    }
                                    else
                                    {
                                        if (mask[j] == 'x' && buffer[bufferIndex] != pattern[j])
                                        {
                                            found = false;
                                            break;
                                        }
                                    }
                                }
                                if (found)
                                {
                                    return start + offset + i;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error if needed
                System.Diagnostics.Debug.WriteLine($"AobScan error: {ex.Message}");
            }
            return IntPtr.Zero;
        }

        public void WriteJump(IntPtr from, IntPtr to)
        {
            int offset = (int)to - (int)from - 5;
            byte[] jmp = new byte[5];
            jmp[0] = 0xE9;
            BitConverter.GetBytes(offset).CopyTo(jmp, 1);
            WriteMemory(from, jmp);
        }

        public bool FreeMemory(IntPtr address)
        {
            return VirtualFreeEx(processHandle, address, 0, 0x8000); // MEM_RELEASE
        }

        public bool ProtectMemory(IntPtr address, uint size, uint newProtect, out uint oldProtect)
        {
            return VirtualProtectEx(processHandle, address, size, newProtect, out oldProtect);
        }

        public byte[] ReadMemory(IntPtr address, int size)
        {
            byte[] buffer = new byte[size];
            ReadProcessMemory(processHandle, address, buffer, size, out _);
            return buffer;
        }

        public bool WriteBytes(IntPtr address, params byte[] bytes)
        {
            return WriteProcessMemory(processHandle, address, bytes, bytes.Length, out _);
        }

        public void CloseProcess()
        {
            if (processHandle != IntPtr.Zero)
            {
                CloseHandle(processHandle);
                processHandle = IntPtr.Zero;
            }
        }

        // Pattern scanning with improved performance
        public IntPtr FindPattern(Process process, string pattern, string mask, IntPtr startAddress = default, int searchSize = 0)
        {
            byte[] patternBytes = StringToByteArray(pattern);
            return AobScan(process, patternBytes, mask);
        }

        private byte[] StringToByteArray(string hex)
        {
            hex = hex.Replace(" ", "").Replace("0x", "");
            return Enumerable.Range(0, hex.Length)
                             .Where(x => x % 2 == 0)
                             .Select(x => Convert.ToByte(hex.Substring(x, 2), 16))
                             .ToArray();
        }
    }
}
