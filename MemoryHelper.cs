using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

namespace ConquerInjector
{
    public class MemoryHelper
    {
        [Flags]
        public enum ProcessAccessFlags : uint
        {
            All = 0x001F0FFF
        }

        [DllImport("kernel32.dll")]
        static extern IntPtr OpenProcess(ProcessAccessFlags dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] lpBuffer, int dwSize, out IntPtr lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] lpBuffer, int dwSize, out IntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flAllocationType, uint flProtect);

        private IntPtr processHandle = IntPtr.Zero;

        public bool OpenProcessById(int pid)
        {
            processHandle = OpenProcess(ProcessAccessFlags.All, false, pid);
            return processHandle != IntPtr.Zero;
        }

        public IntPtr AllocMemory(int size)
        {
            return VirtualAllocEx(processHandle, IntPtr.Zero, (uint)size, 0x1000 | 0x2000, 0x40);
        }

        public void WriteMemory(IntPtr address, byte[] data)
        {
            WriteProcessMemory(processHandle, address, data, data.Length, out _);
        }

        public string ReadPlayerName(Process proc, IntPtr address)
        {
            byte[] buffer = new byte[32];
            ReadProcessMemory(processHandle, address, buffer, buffer.Length, out _);
            return Encoding.ASCII.GetString(buffer).TrimEnd('\0');
        }

        public IntPtr AobScan(Process process, byte[] pattern, string mask)
        {
            foreach (ProcessModule module in process.Modules)
            {
                IntPtr start = module.BaseAddress;
                int size = module.ModuleMemorySize;
                byte[] buffer = new byte[size];

                ReadProcessMemory(processHandle, start, buffer, size, out _);

                for (int i = 0; i < size - pattern.Length; i++)
                {
                    bool found = true;
                    for (int j = 0; j < pattern.Length; j++)
                    {
                        if (mask[j] == 'x' && buffer[i + j] != pattern[j])
                        {
                            found = false;
                            break;
                        }
                    }
                    if (found) return start + i;
                }
            }
            return IntPtr.Zero;
        }

        public void WriteJump(IntPtr from, IntPtr to)
        {
            int offset = (int)to - (int)from - 5;
            byte[] jmp = new byte[5];
            jmp[0] = 0xE9;
            BitConverter.GetBytes(offset).CopyTo(jmp, 1);
            WriteMemory(from, jmp);
        }
    }
}
