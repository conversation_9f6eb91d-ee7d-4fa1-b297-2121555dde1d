using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace ConquerInjector
{
    public static class AssemblerHelper
    {
        private static Dictionary<string, byte[]> instructionMap = new Dictionary<string, byte[]>
        {
            // Basic instructions
            {"push eax", new byte[] {0x50}},
            {"pop eax", new byte[] {0x58}},
            {"pushad", new byte[] {0x60}},
            {"popad", new byte[] {0x61}},
            {"pushfd", new byte[] {0x9C}},
            {"popfd", new byte[] {0x9D}},
            {"ret", new byte[] {0xC3}},
            {"nop", new byte[] {0x90}},
        };

        public static byte[] Assemble(string asmCode, ulong address = 0)
        {
            var lines = asmCode.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                              .Select(l => l.<PERSON>m())
                              .Where(l => !string.IsNullOrEmpty(l) && !l.StartsWith(";"))
                              .ToArray();

            var result = new List<byte>();
            var labels = new Dictionary<string, int>();
            var labelReferences = new List<(int position, string label)>();

            // First pass: collect labels
            int currentPos = 0;
            foreach (var line in lines)
            {
                if (line.EndsWith(":"))
                {
                    var labelName = line.TrimEnd(':');
                    labels[labelName] = currentPos;
                }
                else
                {
                    currentPos += GetInstructionSize(line);
                }
            }

            // Second pass: generate code
            foreach (var line in lines)
            {
                if (line.EndsWith(":"))
                    continue; // Skip labels

                var bytes = AssembleInstruction(line, result.Count, labels, labelReferences);
                result.AddRange(bytes);
            }

            // Third pass: resolve label references
            foreach (var (position, label) in labelReferences)
            {
                if (labels.ContainsKey(label))
                {
                    int offset = labels[label] - (position + 5); // 5 bytes for jmp instruction
                    var offsetBytes = BitConverter.GetBytes(offset);
                    for (int i = 0; i < 4; i++)
                    {
                        result[position + 1 + i] = offsetBytes[i];
                    }
                }
            }

            return result.ToArray();
        }

        private static int GetInstructionSize(string instruction)
        {
            instruction = instruction.Trim().ToLower();

            // Simple instructions
            if (instruction == "push eax" || instruction == "pop eax" || instruction == "ret" || instruction == "nop" ||
                instruction == "pushad" || instruction == "popad" || instruction == "pushfd" || instruction == "popfd")
                return 1;

            // Push with immediate value
            if (instruction.StartsWith("push 0x"))
                return 5;

            // Complex mov instructions
            if (instruction.StartsWith("mov [ebx+0x130], 1056"))
                return 10;
            if (instruction.StartsWith("mov eax, [ebx+0x260]"))
                return 6;
            if (instruction.StartsWith("mov [0x") && instruction.Contains("], ebx"))
                return 6;

            // Complex cmp instructions
            if (instruction.Contains("cmp [ebx+0x260], 0x000f423f"))
                return 10;
            if (instruction.StartsWith("cmp eax, [[0x"))
                return 6;
            if (instruction.StartsWith("cmp [[0x"))
                return 7;
            if (instruction.StartsWith("cmp [ebx+0x268],"))
                return 10;

            // Jump instructions
            if (instruction.StartsWith("je ") || instruction.StartsWith("ja "))
                return 6;
            if (instruction.StartsWith("jmp "))
                return 5;

            return 5; // Default size
        }

        private static byte[] AssembleInstruction(string instruction, int currentPos,
            Dictionary<string, int> labels, List<(int, string)> labelReferences)
        {
            instruction = instruction.Trim();
            var lowerInst = instruction.ToLower();

            // Handle simple instructions
            if (instructionMap.ContainsKey(lowerInst))
                return instructionMap[lowerInst];

            // Handle mov instructions
            if (lowerInst.StartsWith("mov [ebx+0x130], 1056"))
                return new byte[] {0xC7, 0x83, 0x30, 0x01, 0x00, 0x00, 0x20, 0x04, 0x00, 0x00};

            if (lowerInst.Contains("mov eax, [ebx+0x260]"))
                return new byte[] {0x8B, 0x83, 0x60, 0x02, 0x00, 0x00};

            // Handle push with immediate value
            if (lowerInst.StartsWith("push 0x"))
            {
                var addressMatch = Regex.Match(instruction, @"push 0x([0-9A-Fa-f]+)", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address);
                    return new byte[] {0x68}.Concat(addressBytes).ToArray();
                }
            }

            // Handle dynamic mov instructions with addresses
            if (lowerInst.StartsWith("mov [0x") && lowerInst.Contains("], ebx"))
            {
                var addressMatch = Regex.Match(instruction, @"mov \[0x([0-9A-Fa-f]+)\], ebx", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address);
                    return new byte[] {0x89, 0x1D}.Concat(addressBytes).ToArray();
                }
            }

            // Handle cmp instructions
            if (lowerInst.Contains("cmp [ebx+0x260], 0x000f423f"))
                return new byte[] {0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00};

            // Handle dynamic cmp instructions
            if (lowerInst.StartsWith("cmp eax, [[0x") && lowerInst.Contains("]+0x450]"))
            {
                var addressMatch = Regex.Match(instruction, @"cmp eax, \[\[0x([0-9A-Fa-f]+)\]\+0x450\]", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address + 0x450);
                    return new byte[] {0x3B, 0x05}.Concat(addressBytes).ToArray();
                }
            }

            if (lowerInst.StartsWith("cmp [[0x") && lowerInst.Contains("]+0x42c], 6"))
            {
                var addressMatch = Regex.Match(instruction, @"cmp \[\[0x([0-9A-Fa-f]+)\]\+0x42c\], 6", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address + 0x42c);
                    return new byte[] {0x83, 0x3D}.Concat(addressBytes).Concat(new byte[] {0x06}).ToArray();
                }
            }

            if (lowerInst.StartsWith("cmp [[0x") && lowerInst.Contains("]+0x42c], 0"))
            {
                var addressMatch = Regex.Match(instruction, @"cmp \[\[0x([0-9A-Fa-f]+)\]\+0x42c\], 0", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address + 0x42c);
                    return new byte[] {0x83, 0x3D}.Concat(addressBytes).Concat(new byte[] {0x00}).ToArray();
                }
            }

            if (lowerInst.StartsWith("cmp [[0x") && lowerInst.Contains("]+0x428], 35"))
            {
                var addressMatch = Regex.Match(instruction, @"cmp \[\[0x([0-9A-Fa-f]+)\]\+0x428\], 35", RegexOptions.IgnoreCase);
                if (addressMatch.Success)
                {
                    uint address = Convert.ToUInt32(addressMatch.Groups[1].Value, 16);
                    var addressBytes = BitConverter.GetBytes(address + 0x428);
                    return new byte[] {0x83, 0x3D}.Concat(addressBytes).Concat(new byte[] {0x23}).ToArray();
                }
            }

            // Handle cmp [ebx+0x268], value instructions
            var ebxCmpMatch = Regex.Match(lowerInst, @"cmp \[ebx\+0x268\], (\d+)");
            if (ebxCmpMatch.Success)
            {
                int value = int.Parse(ebxCmpMatch.Groups[1].Value);
                var valueBytes = BitConverter.GetBytes(value);
                return new byte[] {0x81, 0xBB, 0x68, 0x02, 0x00, 0x00}.Concat(valueBytes).ToArray();
            }

            // Handle conditional jumps
            if (lowerInst.StartsWith("je "))
            {
                var target = instruction.Substring(3).Trim();

                // Handle absolute address jumps (0x...)
                if (target.StartsWith("0x"))
                {
                    uint address = Convert.ToUInt32(target, 16);
                    int offset = (int)address - (currentPos + 6);
                    var offsetBytes = BitConverter.GetBytes(offset);
                    return new byte[] {0x0F, 0x84}.Concat(offsetBytes).ToArray();
                }
                else
                {
                    // Handle label jumps
                    labelReferences.Add((currentPos, target));
                    return new byte[] {0x0F, 0x84, 0x00, 0x00, 0x00, 0x00}; // Placeholder
                }
            }

            if (lowerInst.StartsWith("ja "))
            {
                var target = instruction.Substring(3).Trim();

                // Handle absolute address jumps (0x...)
                if (target.StartsWith("0x"))
                {
                    uint address = Convert.ToUInt32(target, 16);
                    int offset = (int)address - (currentPos + 6);
                    var offsetBytes = BitConverter.GetBytes(offset);
                    return new byte[] {0x0F, 0x87}.Concat(offsetBytes).ToArray();
                }
                else
                {
                    // Handle label jumps
                    labelReferences.Add((currentPos, target));
                    return new byte[] {0x0F, 0x87, 0x00, 0x00, 0x00, 0x00}; // Placeholder
                }
            }

            if (lowerInst.StartsWith("jmp "))
            {
                var target = instruction.Substring(4).Trim();

                // Handle absolute address jumps (0x...)
                if (target.StartsWith("0x"))
                {
                    uint address = Convert.ToUInt32(target, 16);
                    int offset = (int)address - (currentPos + 5);
                    var offsetBytes = BitConverter.GetBytes(offset);
                    return new byte[] {0xE9}.Concat(offsetBytes).ToArray();
                }
                else
                {
                    // Handle label jumps
                    labelReferences.Add((currentPos, target));
                    return new byte[] {0xE9, 0x00, 0x00, 0x00, 0x00}; // Placeholder
                }
            }

            // For unknown instructions, return NOPs
            return new byte[] {0x90, 0x90, 0x90, 0x90, 0x90}; // NOPs as placeholder
        }
    }
}
