using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace ConquerInjector
{
    public static class AssemblerHelper
    {
        private static Dictionary<string, byte[]> instructionMap = new Dictionary<string, byte[]>
        {
            // Basic instructions
            {"push eax", new byte[] {0x50}},
            {"pop eax", new byte[] {0x58}},
            {"ret", new byte[] {0xC3}},
            {"nop", new byte[] {0x90}},
        };

        public static byte[] Assemble(string asmCode, ulong address = 0)
        {
            var lines = asmCode.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                              .Select(l => l.Trim())
                              .Where(l => !string.IsNullOrEmpty(l) && !l.StartsWith(";"))
                              .ToArray();

            var result = new List<byte>();
            var labels = new Dictionary<string, int>();
            var labelReferences = new List<(int position, string label)>();

            // First pass: collect labels
            int currentPos = 0;
            foreach (var line in lines)
            {
                if (line.EndsWith(":"))
                {
                    var labelName = line.TrimEnd(':');
                    labels[labelName] = currentPos;
                }
                else
                {
                    currentPos += GetInstructionSize(line);
                }
            }

            // Second pass: generate code
            foreach (var line in lines)
            {
                if (line.EndsWith(":"))
                    continue; // Skip labels

                var bytes = AssembleInstruction(line, result.Count, labels, labelReferences);
                result.AddRange(bytes);
            }

            // Third pass: resolve label references
            foreach (var (position, label) in labelReferences)
            {
                if (labels.ContainsKey(label))
                {
                    int offset = labels[label] - (position + 5); // 5 bytes for jmp instruction
                    var offsetBytes = BitConverter.GetBytes(offset);
                    for (int i = 0; i < 4; i++)
                    {
                        result[position + 1 + i] = offsetBytes[i];
                    }
                }
            }

            return result.ToArray();
        }

        private static int GetInstructionSize(string instruction)
        {
            instruction = instruction.Trim().ToLower();

            if (instruction.StartsWith("mov [") && instruction.Contains("], "))
                return 6;
            if (instruction.StartsWith("cmp [") || instruction.StartsWith("mov ["))
                return 6;
            if (instruction.StartsWith("je ") || instruction.StartsWith("jmp "))
                return 5;
            if (instruction.StartsWith("push ") || instruction.StartsWith("pop "))
                return 1;

            return 5; // Default size
        }

        private static byte[] AssembleInstruction(string instruction, int currentPos,
            Dictionary<string, int> labels, List<(int, string)> labelReferences)
        {
            instruction = instruction.Trim();
            var lowerInst = instruction.ToLower();

            // Handle simple instructions
            if (instructionMap.ContainsKey(lowerInst))
                return instructionMap[lowerInst];

            // Handle mov instructions
            if (lowerInst.StartsWith("mov [ebx+0x130], 1056"))
                return new byte[] {0xC7, 0x83, 0x30, 0x01, 0x00, 0x00, 0x20, 0x04, 0x00, 0x00};

            // Handle cmp instructions
            if (lowerInst.Contains("cmp [ebx+0x260], 0x000f423f"))
                return new byte[] {0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00};

            // Handle conditional jumps
            if (lowerInst.StartsWith("je "))
            {
                var label = instruction.Substring(3).Trim();
                labelReferences.Add((currentPos, label));
                return new byte[] {0x0F, 0x84, 0x00, 0x00, 0x00, 0x00}; // Placeholder
            }

            if (lowerInst.StartsWith("ja "))
            {
                var label = instruction.Substring(3).Trim();
                labelReferences.Add((currentPos, label));
                return new byte[] {0x0F, 0x87, 0x00, 0x00, 0x00, 0x00}; // Placeholder
            }

            if (lowerInst.StartsWith("jmp "))
            {
                var label = instruction.Substring(4).Trim();
                labelReferences.Add((currentPos, label));
                return new byte[] {0xE9, 0x00, 0x00, 0x00, 0x00}; // Placeholder
            }

            // Handle complex mov and cmp with dynamic addresses
            if (lowerInst.Contains("mov eax, [ebx+0x260]"))
                return new byte[] {0x8B, 0x83, 0x60, 0x02, 0x00, 0x00};

            // For complex instructions with dynamic addresses, return a placeholder
            return new byte[] {0x90, 0x90, 0x90, 0x90, 0x90}; // NOPs as placeholder
        }
    }
}
