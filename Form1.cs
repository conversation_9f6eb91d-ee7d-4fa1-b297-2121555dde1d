
using System;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;

namespace ConquerBot
{
    public partial class Form1 : Form
    {
        Injector injector = new Injector();

        public Form1()
        {
            InitializeComponent();
            LoadProcesses();
        }

        private void LoadProcesses()
        {
            listBoxProcesses.Items.Clear();
            var procs = Process.GetProcessesByName("Conquer");
            foreach (var p in procs)
            {
                string playerName = injector.ReadPlayerName(p);
                listBoxProcesses.Items.Add(new ProcessItem { Proc = p, DisplayName = $"{playerName} (PID: {p.Id})" });
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadProcesses();
        }

        private void chkEnableBot_CheckedChanged(object sender, EventArgs e)
        {
            var selected = listBoxProcesses.SelectedItem as ProcessItem;
            if (selected == null)
            {
                MessageBox.Show("اختر عملية أولاً");
                chkEnableBot.Checked = false;
                return;
            }

            if (chkEnableBot.Checked)
            {
                bool ok = injector.EnableBot(selected.Proc);
                if (!ok)
                {
                    MessageBox.Show("فشل في تفعيل البوت");
                    chkEnableBot.Checked = false;
                }
            }
            else
            {
                injector.DisableBot(selected.Proc);
            }
        }

        private void listBoxProcesses_SelectedIndexChanged(object sender, EventArgs e)
        {
            chkEnableBot.Checked = false;
        }
    }

    public class ProcessItem
    {
        public Process Proc { get; set; }
        public string DisplayName { get; set; }
        public override string ToString() => DisplayName;
    }
}
