
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;

namespace ConquerInjector
{
    public partial class Form1 : Form
    {
        private class GameProcessInfo
        {
            public Process Process;
            public string PlayerName;
            public IntPtr HookAddress = IntPtr.Zero;
            public IntPtr ShellcodeAddress = IntPtr.Zero;
            public IntPtr MonstersB = IntPtr.Zero;
        }

        private List<GameProcessInfo> gameProcesses = new List<GameProcessInfo>();
        private MemoryHelper mem = new MemoryHelper();

        public Form1()
        {
            InitializeComponent();
            RefreshProcesses();
        }

        private void buttonRefresh_Click(object sender, EventArgs e)
        {
            RefreshProcesses();
        }

        private void RefreshProcesses()
        {
            listBoxProcesses.Items.Clear();
            gameProcesses.Clear();

            var processes = Process.GetProcessesByName("Conquer");
            foreach (var proc in processes)
            {
                if (mem.OpenProcessById(proc.Id))
                {
                    IntPtr playerNameAddr = proc.MainModule.BaseAddress + 0x14FC7C4;
                    string playerName = mem.ReadPlayerName(proc, playerNameAddr);
                    if (string.IsNullOrWhiteSpace(playerName))
                        playerName = "Unknown";

                    gameProcesses.Add(new GameProcessInfo
                    {
                        Process = proc,
                        PlayerName = playerName
                    });

                    listBoxProcesses.Items.Add($"{playerName} (PID: {proc.Id})");
                }
            }
            Log($"تم تحديث قائمة العمليات، العدد: {gameProcesses.Count}");
        }

        private GameProcessInfo GetSelectedProcessInfo()
        {
            if (listBoxProcesses.SelectedIndex == -1) return null;
            return gameProcesses[listBoxProcesses.SelectedIndex];
        }

        private void buttonEnable_Click(object sender, EventArgs e)
        {
            var info = GetSelectedProcessInfo();
            if (info == null)
            {
                MessageBox.Show("الرجاء اختيار عملية أولاً.");
                return;
            }

            if (!mem.OpenProcessById(info.Process.Id))
            {
                MessageBox.Show("فشل فتح العملية.");
                return;
            }

            // البحث عن النمط المحدد في الذاكرة - العناوين والقيم مضمونة من السكربت الأصلي
            byte[] pattern = { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00, 0x77, 0x0D };
            string mask = "xxxxxxxxxxxx";

            IntPtr hookAddr = mem.AobScan(info.Process, pattern, mask);
            if (hookAddr == IntPtr.Zero)
            {
                MessageBox.Show("تعذر العثور على نمط التتبع (AOB) في الذاكرة.");
                return;
            }

            IntPtr monstersB = mem.AllocMemory(0x16);
            if (monstersB == IntPtr.Zero)
            {
                MessageBox.Show("فشل تخصيص ذاكرة للMonstersB.");
                return;
            }

            IntPtr shellcodeAddr = mem.AllocMemory(0x1000);
            if (shellcodeAddr == IntPtr.Zero)
            {
                MessageBox.Show("فشل تخصيص ذاكرة للكود.");
                return;
            }

            uint baseAddr = (uint)info.Process.MainModule.BaseAddress.ToInt32() + 0x14F1D80;

            // كود ASM المحسن مع العناوين الصحيحة
            string asmCode = $@"
push eax
mov eax, [ebx+0x260]
cmp eax, [[0x{baseAddr:X}]+0x450]
je MotionStatus
jmp code

MotionStatus:
cmp [[0x{baseAddr:X}]+0x42C], 6
je GoMonsterStatus
cmp [[0x{baseAddr:X}]+0x42C], 0
je GoMonsterStatus
jmp code

GoMonsterStatus:
cmp [[0x{baseAddr:X}]+0x428], 35
je DeadStatus
jmp code

DeadStatus:
cmp [ebx+0x268], 3992
je code
cmp [ebx+0x268], 3986
je code
cmp [ebx+0x268], 3984
je code
cmp [ebx+0x268], 3983
je code
cmp [ebx+0x268], 3982
je code
cmp [ebx+0x268], 3981
je code
cmp [ebx+0x268], 3980
je code
cmp [ebx+0x268], 3979
je code
mov [ebx+0x130], 1056
jmp code

code:
pop eax
cmp [ebx+0x260], 0x000F423F
ja return
mov [0x{monstersB.ToInt32():X}], ebx
jmp return

return:
";

            // استخدام AssemblerHelper المحسن لتجميع الكود
            byte[] shellcode = AssemblerHelper.Assemble(asmCode);

            if (shellcode == null || shellcode.Length == 0)
            {
                MessageBox.Show("فشل تجميع كود الـASM.");
                return;
            }

            mem.WriteMemory(shellcodeAddr, shellcode);
            mem.WriteJump(hookAddr, shellcodeAddr);

            info.HookAddress = hookAddr;
            info.ShellcodeAddress = shellcodeAddr;
            info.MonstersB = monstersB;

            Log($"تم تفعيل السكربت على اللاعب: {info.PlayerName}");
        }

        private void buttonDisable_Click(object sender, EventArgs e)
        {
            var info = GetSelectedProcessInfo();
            if (info == null || info.HookAddress == IntPtr.Zero)
            {
                MessageBox.Show("لا يوجد سكربت مفعّل للعملية المختارة.");
                return;
            }

            if (!mem.OpenProcessById(info.Process.Id))
            {
                MessageBox.Show("فشل فتح العملية.");
                return;
            }

            // استعادة البايتات الأصلية
            byte[] originalBytes = { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00, 0x77, 0x0D };
            mem.WriteMemory(info.HookAddress, originalBytes);

            // تحرير الذاكرة المخصصة
            if (info.ShellcodeAddress != IntPtr.Zero)
                mem.FreeMemory(info.ShellcodeAddress);
            if (info.MonstersB != IntPtr.Zero)
                mem.FreeMemory(info.MonstersB);

            Log($"تم إيقاف السكربت على اللاعب: {info.PlayerName}");

            info.HookAddress = IntPtr.Zero;
            info.ShellcodeAddress = IntPtr.Zero;
            info.MonstersB = IntPtr.Zero;
        }

        private void Log(string msg)
        {
            if (textBoxLog.InvokeRequired)
            {
                textBoxLog.Invoke(new Action(() => textBoxLog.AppendText(msg + Environment.NewLine)));
            }
            else
            {
                textBoxLog.AppendText(msg + Environment.NewLine);
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // تنظيف الموارد عند إغلاق التطبيق
            mem.CloseProcess();
            base.OnFormClosed(e);
        }
    }
}
