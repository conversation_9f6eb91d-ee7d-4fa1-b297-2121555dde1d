
namespace ConquerInjector
{
    partial class Form1
    {

        private System.Windows.Forms.ListBox listBoxProcesses;
        private System.Windows.Forms.Button buttonRefresh;
        private System.Windows.Forms.Button buttonEnable;
        private System.Windows.Forms.Button buttonDisable;
        private System.Windows.Forms.TextBox textBoxLog;

        private void InitializeComponent()
        {
            this.listBoxProcesses = new System.Windows.Forms.ListBox();
            this.buttonRefresh = new System.Windows.Forms.Button();
            this.buttonEnable = new System.Windows.Forms.Button();
            this.buttonDisable = new System.Windows.Forms.Button();
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.SuspendLayout();

            this.listBoxProcesses.FormattingEnabled = true;
            this.listBoxProcesses.Location = new System.Drawing.Point(12, 12);
            this.listBoxProcesses.Size = new System.Drawing.Size(260, 160);

            this.buttonRefresh.Text = "تحديث";
            this.buttonRefresh.Location = new System.Drawing.Point(12, 180);
            this.buttonRefresh.Click += new System.EventHandler(this.buttonRefresh_Click);

            this.buttonEnable.Text = "تفعيل";
            this.buttonEnable.Location = new System.Drawing.Point(90, 180);
            this.buttonEnable.Click += new System.EventHandler(this.buttonEnable_Click);

            this.buttonDisable.Text = "إيقاف";
            this.buttonDisable.Location = new System.Drawing.Point(180, 180);
            this.buttonDisable.Click += new System.EventHandler(this.buttonDisable_Click);

            this.textBoxLog.Multiline = true;
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Location = new System.Drawing.Point(12, 220);
            this.textBoxLog.Size = new System.Drawing.Size(260, 120);
            this.textBoxLog.ReadOnly = true;

            this.ClientSize = new System.Drawing.Size(284, 361);
            this.Controls.Add(this.listBoxProcesses);
            this.Controls.Add(this.buttonRefresh);
            this.Controls.Add(this.buttonEnable);
            this.Controls.Add(this.buttonDisable);
            this.Controls.Add(this.textBoxLog);
            this.Name = "Form1";
            this.Text = "Conquer Injector";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
