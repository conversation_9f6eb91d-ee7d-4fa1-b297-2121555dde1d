
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using Keystone.NET;

namespace ConquerBot
{
    public class Injector
    {
        [DllImport("kernel32.dll")]
        static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] buffer, int size, out int bytesRead);

        [DllImport("kernel32.dll")]
        static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] buffer, int size, out int bytesWritten);

        [DllImport("kernel32.dll")]
        static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll")]
        static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint dwFreeType);

        const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
        const uint MEM_COMMIT = 0x1000;
        const uint MEM_RELEASE = 0x8000;
        const uint PAGE_EXECUTE_READWRITE = 0x40;

        Keystone keystone;

        private class InjectedData
        {
            public IntPtr ShellcodeAddress;
            public IntPtr MonstersBAddress;
            public byte[] OriginalBytes;
        }

        private readonly System.Collections.Generic.Dictionary<int, InjectedData> injected = new();

        public Injector()
        {
            keystone = new Keystone(Architecture.X86, Mode.Bit32);
        }

        public string ReadPlayerName(Process proc)
        {
            try
            {
                IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, proc.Id);
                if (hProc == IntPtr.Zero) return "Unknown";

                IntPtr baseAddr = proc.MainModule.BaseAddress;
                IntPtr playerNameAddr = IntPtr.Add(baseAddr, 0x14FC7C4);

                byte[] buffer = new byte[32];
                if (!ReadProcessMemory(hProc, playerNameAddr, buffer, buffer.Length, out int bytesRead))
                    return "Unknown";

                string name = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                name = name.TrimEnd('\0');

                return name;
            }
            catch { return "Unknown"; }
        }

        public bool EnableBot(Process proc)
        {
            if (injected.ContainsKey(proc.Id)) return true;

            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, proc.Id);
            if (hProc == IntPtr.Zero) return false;

            IntPtr baseAddr = proc.MainModule.BaseAddress;
            ulong baseAddress = (ulong)baseAddr.ToInt64();

            string asm = $@"
push eax
mov eax,[ebx+0x260]
cmp eax, [[0x{(baseAddress + 0x14F1D80):X}]+0x450]
je MotionStatus
jmp code

MotionStatus:
cmp [[0x{(baseAddress + 0x14F1D80):X}]+0x42C], 6
je GoMonsterStatus
cmp [[0x{(baseAddress + 0x14F1D80):X}]+0x42C], 0
je GoMonsterStatus
jmp code

GoMonsterStatus:
cmp [[0x{(baseAddress + 0x14F1D80):X}]+0x428], 35
je DeadStatus
jmp code

DeadStatus:
cmp [ebx+0x268], 3992
je code
cmp [ebx+0x268], 3986
je code
cmp [ebx+0x268], 3984
je code
cmp [ebx+0x268], 3983
je code
cmp [ebx+0x268], 3982
je code
cmp [ebx+0x268], 3981
je code
cmp [ebx+0x268], 3980
je code
cmp [ebx+0x268], 3979
je code
mov [ebx+0x130], 1056
jmp code

code:
pop eax
cmp [ebx+0x260], 0x000F423F
ja return
mov [MonstersB], ebx
jmp return

return:
";

            byte[] shellcode = keystone.Assemble(asm);

            IntPtr shellcodeMem = VirtualAllocEx(hProc, IntPtr.Zero, (uint)shellcode.Length, MEM_COMMIT, PAGE_EXECUTE_READWRITE);
            IntPtr monstersBMem = VirtualAllocEx(hProc, IntPtr.Zero, 0x16, MEM_COMMIT, PAGE_EXECUTE_READWRITE);

            if (shellcodeMem == IntPtr.Zero || monstersBMem == IntPtr.Zero) return false;

            WriteProcessMemory(hProc, shellcodeMem, shellcode, shellcode.Length, out int _);

            // FIXME: Need real pattern scan here. Return IntPtr.Zero for now.
            IntPtr hookAddr = IntPtr.Zero;

            if (hookAddr == IntPtr.Zero)
                return false;

            byte[] originalBytes = new byte[12];
            ReadProcessMemory(hProc, hookAddr, originalBytes, originalBytes.Length, out int _);

            byte[] jmpShellcode = CreateJmp(shellcodeMem, hookAddr);

            WriteProcessMemory(hProc, hookAddr, jmpShellcode, jmpShellcode.Length, out int _);

            injected[proc.Id] = new InjectedData
            {
                ShellcodeAddress = shellcodeMem,
                MonstersBAddress = monstersBMem,
                OriginalBytes = originalBytes
            };

            return true;
        }

        public bool DisableBot(Process proc)
        {
            if (!injected.ContainsKey(proc.Id)) return false;

            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, proc.Id);
            if (hProc == IntPtr.Zero) return false;

            var data = injected[proc.Id];

            WriteProcessMemory(hProc, data.ShellcodeAddress, data.OriginalBytes, data.OriginalBytes.Length, out int _);

            VirtualFreeEx(hProc, data.ShellcodeAddress, 0, MEM_RELEASE);
            VirtualFreeEx(hProc, data.MonstersBAddress, 0, MEM_RELEASE);

            injected.Remove(proc.Id);
            return true;
        }

        private byte[] CreateJmp(IntPtr target, IntPtr src)
        {
            long offset = (long)target - (long)src - 5;
            byte[] jmp = new byte[5];
            jmp[0] = 0xE9;
            BitConverter.GetBytes((int)offset).CopyTo(jmp, 1);
            return jmp;
        }
    }
}
